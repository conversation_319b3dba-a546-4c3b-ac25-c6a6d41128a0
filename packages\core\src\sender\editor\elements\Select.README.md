# Slate Select Component with API Support

Slate编辑器的Select组件，现在支持通过接口动态获取选项数据。

## 功能特性

- ✅ 支持静态选项数据传入
- ✅ 支持通过接口动态获取选项数据
- ✅ 支持多种 HTTP 方法（GET、POST、PUT、DELETE、PATCH）
- ✅ 支持自定义请求头和参数
- ✅ 支持请求取消和错误处理
- ✅ 支持加载状态显示
- ✅ 静态数据和接口数据可以同时使用
- ✅ 与现有的Slate编辑器完全兼容
- ✅ 向后兼容原有TipTap接口

## 使用方式

### 1. 仅使用静态数据（原有功能）

```javascript
// 使用HTML标签方式插入（向后兼容）
const selectText = `<embedded-select placeholder="请选择企业类型" options="[{&quot;label&quot;:&quot;有限责任公司&quot;,&quot;value&quot;:&quot;limited_company&quot;},{&quot;label&quot;:&quot;股份有限公司&quot;,&quot;value&quot;:&quot;joint_stock_company&quot;}]" defaultValue="" tooltips="选择企业的法律组织形式" disabled="false"></embedded-select>`;
editorRef.current?.insertText(selectText);

// 或使用Slate插件方式
import { insertSelect } from './slate-plugins';
insertSelect(editor, [
  { label: "有限责任公司", value: "limited_company" },
  { label: "股份有限公司", value: "joint_stock_company" },
  { label: "个人独资企业", value: "sole_proprietorship" },
  { label: "合伙企业", value: "partnership" },
  { label: "外商投资企业", value: "foreign_investment" },
], "请选择企业类型", "", "选择企业的法律组织形式", false);
```

### 2. 仅使用接口数据（新功能）

```javascript
editor.chain().focus().insertContent({
  type: 'Select',
  attrs: {
    placeholder: "请选择选项",
    apiConfig: {
      url: "/api/options",
      method: "GET",
      headers: {
        "Authorization": "Bearer token"
      },
      params: {
        category: "type1"
      }
    },
    tooltips: "从接口获取选项数据",
    disabled: false,
  }
}).run();
```

### 3. 同时使用静态数据和接口数据（新功能）

```javascript
editor.chain().focus().insertContent({
  type: 'Select',
  attrs: {
    placeholder: "请选择选项",
    options: [
      { label: "默认选项", value: "default" }
    ],
    apiConfig: {
      url: "/api/dynamic-options",
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      data: {
        filter: "active"
      }
    },
    tooltips: "静态选项 + 动态接口选项",
    disabled: false,
  }
}).run();
```

## API 配置

### ApiConfig 接口

```typescript
interface ApiConfig {
  /** 接口地址 */
  url: string;
  /** 请求方法，默认为 GET */
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求参数（用于 GET 和 DELETE） */
  params?: Record<string, unknown>;
  /** 请求体数据（用于 POST、PUT、PATCH） */
  data?: Record<string, unknown>;
}
```

### 接口返回数据格式

组件支持多种接口返回数据格式：

#### 格式1：直接返回数组
```json
[
  { "label": "选项1", "value": "value1" },
  { "label": "选项2", "value": "value2" }
]
```

#### 格式2：包装在 options 字段中
```json
{
  "options": [
    { "label": "选项1", "value": "value1" },
    { "label": "选项2", "value": "value2" }
  ]
}
```

#### 格式3：包装在 data 字段中
```json
{
  "data": [
    { "label": "选项1", "value": "value1" },
    { "label": "选项2", "value": "value2" }
  ]
}
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| placeholder | `string` | "Please select..." | 占位符文本 |
| options | `Array<{label: string, value: string}>` | `[]` | 静态选项数据 |
| apiConfig | `ApiConfig` | - | 接口配置 |
| defaultValue | `string` | - | 默认选中值 |
| tooltips | `string` | - | 提示信息 |
| disabled | `boolean` | `false` | 是否禁用 |

## 状态管理

- **加载状态**：当接口请求进行中时，组件会显示加载状态
- **错误处理**：接口请求失败时会显示错误信息
- **请求取消**：组件卸载时会自动取消进行中的请求

## 注意事项

1. 静态 `options` 和接口返回的选项会合并显示
2. 接口请求在组件挂载时自动触发
3. 当 `apiConfig` 发生变化时会重新发起请求
4. 组件会自动处理请求取消，避免内存泄漏
5. 支持项目现有的请求拦截器和错误处理机制
6. 与现有的Slate编辑器功能完全兼容
7. 向后兼容原有TipTap接口，无需修改现有代码

## 技术实现

- 基于Slate.js构建，提供更好的扩展性和性能
- 使用项目现有的 `@/request` 模块进行HTTP请求
- 支持 AbortController 进行请求取消
- 遵循项目现有的代码架构和设计模式
- 完全向后兼容，不影响现有功能
